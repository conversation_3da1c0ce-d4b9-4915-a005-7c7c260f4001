import { Project } from "@/types";
import { Metadata } from "next";

interface CreateProjectPageProps {
  params: Promise<{ slug: string }>;
}

export const metadata: Metadata = {
  title: "Projects",
  description:
    "Manage your projects efficiently with our intuitive project management tools.",
};

const CreateProjectPage = async ({ params }: CreateProjectPageProps) => {
  const { slug } = await params;

  const res = await fetch(
    `${process.env.NEXT_PUBLIC_BASE_API}/project/${slug}`,
    {
      cache: "no-store",
    }
  );
  const { data } = (await res.json()) as Project;

  console.log("res:", data);

  return (
    <div className="container mx-auto px-4 py-20">
      CreateProjectPage {slug} params
    </div>
  );
};

export default CreateProjectPage;
